'use client';

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import type { PaymentFormRef } from './StripePaymentElement';

interface StickyPaymentButtonProps {
    isLoggedIn: boolean;
    paymentMethod: string;
    showCardForm: boolean;
    totalAmount: number;
    isPaymentMethodSelected: boolean;
    paymentFormRef?: React.RefObject<PaymentFormRef>;
    onPaymentClick: () => void;
    onShowCardForm: () => void;
    onShowLoginPrompt: () => void;
    getPaymentMethodName: (methodId: string) => string;
}

const StickyPaymentButton: React.FC<StickyPaymentButtonProps> = ({
    isLoggedIn,
    paymentMethod,
    showCardForm,
    totalAmount,
    isPaymentMethodSelected,
    paymentFormRef,
    onPaymentClick,
    onShowCardForm,
    onShowLoginPrompt,
    getPaymentMethodName
}) => {
    const { t } = useTranslation();
    const [isDesktop, setIsDesktop] = useState(false);

    // Get processing state from payment form ref
    const isProcessing = paymentFormRef?.current?.isProcessing || false;

    // Ekran boyutunu kontrol et
    useEffect(() => {
        const checkIfDesktop = () => {
            setIsDesktop(window.innerWidth >= 768);
        };

        checkIfDesktop();
        window.addEventListener('resize', checkIfDesktop);

        return () => {
            window.removeEventListener('resize', checkIfDesktop);
        };
    }, []);

    const scrollToPaymentForm = () => {
        const paymentSection = document.querySelector('.stripe-payment-section');
        if (paymentSection) {
            paymentSection.scrollIntoView({ 
                behavior: 'smooth',
                block: 'center'
            });
        }
    };

    const handlePaymentButtonClick = () => {
        if (!isLoggedIn) {
            // Kullanıcı giriş yapmamışsa, giriş uyarısını göster
            onShowLoginPrompt();

            // Giriş bölümüne scroll et
            setTimeout(() => {
                const loginSection = document.querySelector('.login-warning');
                if (loginSection) {
                    loginSection.scrollIntoView({ behavior: 'smooth' });
                    // Vurgu efekti ekle
                    loginSection.classList.add('highlight-section');
                    setTimeout(() => {
                        loginSection.classList.remove('highlight-section');
                    }, 1500);
                }
            }, 100);
            return;
        }

        if (paymentMethod === 'credit_card') {
            // Stripe ödeme formu varsa
            if (paymentFormRef?.current) {
                // Önce forma scroll et
                scrollToPaymentForm();

                // Kısa bir gecikme sonra formu submit et
                setTimeout(() => {
                    paymentFormRef.current?.submitForm();
                }, 300);
            } else if (!showCardForm) {
                // Kart formu henüz gösterilmiyorsa, önce göster
                onShowCardForm();

                // Kart formuna scroll et
                setTimeout(() => {
                    const cardFormSection = document.querySelector('.card-form-section');
                    if (cardFormSection) {
                        cardFormSection.scrollIntoView({ behavior: 'smooth' });
                    }
                }, 100);
            } else {
                // Fallback: normal ödeme işlemi
                onPaymentClick();
            }
        } else if (paymentMethod === 'applepay') {
            // Apple Pay için özel işlem
            // Burada Apple Pay API'sini çağırabilirsiniz
            console.log('Apple Pay selected');
            onPaymentClick();
        } else if (paymentMethod === 'googlepay') {
            // Google Pay için özel işlem
            // Burada Google Pay API'sini çağırabilirsiniz
            console.log('Google Pay selected');
            onPaymentClick();
        } else {
            // Diğer ödeme yöntemleri için normal işlem
            onPaymentClick();
        }
    };

    const getButtonText = () => {
        if (isProcessing) {
            return t('checkout.payment.processing');
        }
        
        if (!isPaymentMethodSelected) {
            return t('checkout.completePayment');
        }
        
        if (!isLoggedIn) {
            return t('checkout.loginToPayment');
        }
        
        return t('checkout.payWith', { method: getPaymentMethodName(paymentMethod) });
    };

    const isButtonDisabled = () => {
        return !isPaymentMethodSelected || isProcessing;
    };

    return (
        <div 
            className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 z-40 rounded-t-2xl" 
            style={isDesktop ? { 
                maxWidth: '1000px', 
                margin: '0 auto', 
                left: '50%', 
                transform: 'translateX(-50%)' 
            } : {}}
        >
            <div className="flex justify-between items-center mb-2">
                <span className="text-gray-600">{t('checkout.total')}</span>
                <span className="font-bold text-lg">${totalAmount.toFixed(2)}</span>
            </div>

            <button
                className={`w-full py-3 px-4 rounded-3xl font-semibold text-lg transition-colors ${
                    isButtonDisabled()
                        ? 'bg-gray-300 text-gray-600 cursor-not-allowed'
                        : 'bg-yellow-400 text-black hover:bg-yellow-500'
                }`}
                onClick={handlePaymentButtonClick}
                disabled={isButtonDisabled()}
            >
                {getButtonText()}
            </button>
        </div>
    );
};

export default StickyPaymentButton;
