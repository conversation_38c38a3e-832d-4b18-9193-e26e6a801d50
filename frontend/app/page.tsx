'use client';

import React from 'react';
const useState = React.useState;
const useEffect = React.useEffect;
import { FiSearch, FiChevronRight } from 'react-icons/fi';
import Link from 'next/link';
import Image from 'next/image';
import ESIMCompatibilityModal from './components/ESIMCompatibilityModal';
import ESIMReadyScreen from './components/ESIMReadyScreen';
import PopularCountriesSlider from './components/PopularCountriesSlider';
import CountryFlag from './components/CountryFlag';
import CompatibilityCard from './components/CompatibilityCard';
import { useUser } from "@/context/UserContext";
import { useTranslation } from 'react-i18next';
import { normalizeForSearch } from '@/utils/stringUtils';
import api from '@/lib/api';

interface Country {
    id: string;
    name: string;
    code: string;
    flag?: string;
    isFeatured?: boolean;
}

export default function Home() {
    const { t, i18n } = useTranslation();
    const [searchQuery, setSearchQuery] = useState('');
    const [showResults, setShowResults] = useState(false);
    const [filteredCountries, setFilteredCountries] = useState<Country[]>([]);
    const [showCompatibilityModal, setShowCompatibilityModal] = useState(false);
    const [deviceSearchQuery, setDeviceSearchQuery] = useState('');
    const [filteredDevices, setFilteredDevices] = useState<string[]>([]);
    const [showSuccessScreen, setShowSuccessScreen] = useState(false);

    // Ülkeleri API'den çekmek için state'ler
    const [popularCountries, setPopularCountries] = useState<Country[]>([]);
    const [allCountries, setAllCountries] = useState<Country[]>([]);
    const [isLoadingCountries, setIsLoadingCountries] = useState(true);

    // API'den ülkeleri çek
    useEffect(() => {
        const fetchCountries = async () => {
            try {
                setIsLoadingCountries(true);

                // Tüm ülkeleri çek
                const response = await api.get('/api/countries', {
                    params: {
                        lang: i18n.language
                    }
                });

                if (response.data && Array.isArray(response.data)) {
                    // API'den gelen verileri Country tipine dönüştür
                    const countries = response.data.map((country: any) => ({
                        id: country.slug.replace('-esim', ''),
                        name: t(`regions:countries.${country.code.toUpperCase()}.name`),
                        code: country.code.toLowerCase(),
                        isFeatured: country.is_featured,
                    }));

                    // Tüm ülkeleri ayarla
                    setAllCountries(countries);

                    // Popüler ülkeleri filtrele
                    const featured = countries.filter(country => country.isFeatured);
                    setPopularCountries(featured);
                }
            } catch (error) {
                console.error('Ülkeler alınamadı:', error);
                // Hata durumunda varsayılan ülkeleri kullan
                setPopularCountries([
                    { id: 'sc', name: t('countries.sc', 'Seychelles'), code: 'sc' },
                    { id: 'cn', name: t('countries.cn', 'China'), code: 'cn' },
                    { id: 'br', name: t('countries.br', 'Brazil'), code: 'br' },
                    { id: 'us', name: t('countries.us', 'USA'), code: 'us' },
                    { id: 'ca', name: t('countries.ca', 'Canada'), code: 'ca' },
                    { id: 'uk', name: t('countries.gb', 'United Kingdom'), code: 'gb' },
                    { id: 'au', name: t('countries.au', 'Australia'), code: 'au' },
                ]);

                setAllCountries([
                    { id: 'uk', name: t('countries.gb', 'United Kingdom'), code: 'gb' },
                    { id: 'de', name: t('countries.de', 'Germany'), code: 'de' },
                    { id: 'fr', name: t('countries.fr', 'France'), code: 'fr' },
                    { id: 'es', name: t('countries.es', 'Spain'), code: 'es' },
                    { id: 'it', name: t('countries.it', 'Italy'), code: 'it' },
                    { id: 'jp', name: t('countries.jp', 'Japan'), code: 'jp' },
                    { id: 'cn', name: t('countries.cn', 'China'), code: 'cn' },
                    { id: 'br', name: t('countries.br', 'Brazil'), code: 'br' },
                    { id: 'tr', name: t('countries.tr', 'Turkey'), code: 'tr' },
                    { id: 'mx', name: t('countries.mx', 'Mexico'), code: 'mx' },
                    { id: 'kr', name: t('countries.kr', 'South Korea'), code: 'kr' },
                ]);
            } finally {
                setIsLoadingCountries(false);
            }
        };

        fetchCountries();
    }, [t]);

    // Cihaz listesi
    const compatibleDevices = [
        'iPhone 14 Pro Max', 'iPhone 14 Pro', 'iPhone 14 Plus', 'iPhone 14',
        'iPhone 13 Pro Max', 'iPhone 13 Pro', 'iPhone 13 mini', 'iPhone 13',
        'iPhone 12 Pro Max', 'iPhone 12 Pro', 'iPhone 12 mini', 'iPhone 12',
        'iPhone SE (2022)', 'iPhone 11 Pro Max', 'iPhone 11 Pro', 'iPhone 11',
        'Samsung Galaxy S23 Ultra', 'Samsung Galaxy S23+', 'Samsung Galaxy S23',
        'Samsung Galaxy S22 Ultra', 'Samsung Galaxy S22+', 'Samsung Galaxy S22',
        'Samsung Galaxy Z Fold4', 'Samsung Galaxy Z Flip4',
        'Google Pixel 7 Pro', 'Google Pixel 7', 'Google Pixel 6 Pro', 'Google Pixel 6',
    ];

    // Arama için normalizeForSearch fonksiyonu utils/stringUtils.ts'den import edildi

    // Arama yapıldıkça filtreleme (ülkeler)
    useEffect(() => {
        if (searchQuery.trim() === '') {
            setFilteredCountries([]);
            setShowResults(false);
            return;
        }

        const normalizedQuery = normalizeForSearch(searchQuery);
        const filtered = allCountries.filter(country => {
            const normalizedCountryName = normalizeForSearch(country.name);
            return normalizedCountryName.includes(normalizedQuery);
        });

        setFilteredCountries(filtered);
        setShowResults(true);
    }, [searchQuery]);

    // Cihaz araması
    useEffect(() => {
        if (deviceSearchQuery.trim() === '') {
            setFilteredDevices(compatibleDevices);
            return;
        }

        const normalizedQuery = normalizeForSearch(deviceSearchQuery);
        const filtered = compatibleDevices.filter(device => {
            const normalizedDeviceName = normalizeForSearch(device);
            return normalizedDeviceName.includes(normalizedQuery);
        });

        setFilteredDevices(filtered);
    }, [deviceSearchQuery]);

    // Arama işlemi
    const handleSearch = () => {
        if (searchQuery.trim() === '') return;
        alert(`"${searchQuery}" için arama yapıldı`);
    };

    // Bir ülke seçildiğinde
    const handleSelectCountry = (country: Country) => {
        setSearchQuery(country.name);
        setShowResults(false);
        // Paketer sayfasına yönlendirme
        window.location.href = `/${i18n.language}/${country.id}-esim`;
    };

    // Uyumluluk modalını aç
    const openCompatibilityModal = () => {
        setShowCompatibilityModal(true);
    };

    // Uyumluluk modalını kapat
    const closeCompatibilityModal = () => {
        setShowCompatibilityModal(false);
    };

    // Success screen demo
    const openSuccessScreen = () => {
        setShowSuccessScreen(true);
    };

    const closeSuccessScreen = () => {
        setShowSuccessScreen(false);
    };

    const { user } = useUser();

    return (
        <div className="app-container pt-6 rounded-t-2xl md:rounded-t-none">
            {/* Merhaba yazısı */}
            <div className="flex justify-between items-center mb-4 md:mb-6">
                <div className="text-lg md:text-xl font-medium text-gray-700">{t('common.hello', 'Merhaba')} {user ? user.name : ''}</div>
            </div>

            {/* Hero Title Section */}
            <div className="text-center mb-8 md:mb-10 px-2 md:px-4">
                <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-800 mb-3 leading-tight">
                    {t('home.heroTitle', 'Saniyeler İçinde Bağlan, Dünyayla Temasta Kal.')}
                </h1>
                <p className="text-gray-600 md:text-lg max-w-2xl mx-auto">
                    {t('home.heroSubtitle', 'Fiyatlar net, bağlantı hızlı. eSIM\'le tanış, karmaşadan kurtul.')}
                </p>
            </div>

            {/* Arama Kutusu */}
            <div className="relative mb-8 md:mb-10 md:max-w-2xl md:mx-auto px-2 md:px-0">
                <div className="search-container hover:shadow-lg transition-shadow duration-300">
                    <input
                        type="text"
                        placeholder={t('home.searchPlaceholder', 'Yolculuk nereye?')}
                        className="search-input pl-6 pr-14 md:py-4 md:text-lg border-2 border-gray-100 focus:border-yellow-300"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                    />
                    <button
                        className="absolute inset-y-0 right-2 flex items-center justify-center w-10 h-10 my-auto bg-yellow-400 hover:bg-yellow-500 rounded-full md:w-12 md:h-12 transition-colors duration-300"
                        onClick={handleSearch}
                    >
                        <FiSearch className="h-5 w-5 text-black md:h-6 md:w-6" />
                    </button>
                </div>

                {/* Otomatik tamamlama listesi */}
                {showResults && filteredCountries.length > 0 && (
                    <div className="absolute left-0 right-0 top-full mt-1 bg-white rounded-lg shadow-lg z-50 border border-gray-200 max-h-60 overflow-y-auto md:max-h-80">
                        {filteredCountries.map((country: Country) => (
                            <div
                                key={country.id}
                                className="flex items-center px-4 py-3 cursor-pointer hover:bg-gray-50 border-b border-gray-100 last:border-b-0 md:py-4"
                                onClick={() => handleSelectCountry(country)}
                            >
                                <div className="ml-1 mr-4 w-6 h-6 md:w-8 md:h-8">
                                    <CountryFlag
                                        countryCode={country.code}
                                        width={24}
                                        alt={country.name}
                                    />
                                </div>
                                <span className="md:text-lg">{country.name}</span>
                            </div>
                        ))}
                    </div>
                )}
            </div>

            {/* Uyumluluk Kontrolü */}
            <CompatibilityCard onCheckClick={openCompatibilityModal} />

            {/* Popüler Ülkeler */}
            <PopularCountriesSlider countries={popularCountries} />

            {/* Tüm Ülkeler */}
            <h2 className="text-2xl font-bold mb-4 text-gray-700 md:text-2xl md:mb-5">{t('home.allCountries', 'Tüm Ülkeler')}</h2>

            {/* Mobil görünüm için liste */}
            <div className="bg-gray-100 rounded-xl overflow-hidden md:hidden">
                {isLoadingCountries ? (
                    // Yükleme durumunda gösterilecek içerik
                    <div className="p-4 text-center">
                        <div className="animate-pulse flex space-x-4">
                            <div className="rounded-full bg-gray-200 h-10 w-10"></div>
                            <div className="flex-1 space-y-4 py-1">
                                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                            </div>
                        </div>
                        <div className="animate-pulse flex space-x-4 mt-4">
                            <div className="rounded-full bg-gray-200 h-10 w-10"></div>
                            <div className="flex-1 space-y-4 py-1">
                                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                            </div>
                        </div>
                        <div className="animate-pulse flex space-x-4 mt-4">
                            <div className="rounded-full bg-gray-200 h-10 w-10"></div>
                            <div className="flex-1 space-y-4 py-1">
                                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                            </div>
                        </div>
                    </div>
                ) : (
                    // Ülkeler yüklendiğinde gösterilecek içerik (mobil)
                    allCountries.slice(0, 15).map(country => (
                        <Link href={`/${i18n.language}/${country.id}-esim`} key={country.id}>
                            <div className="country-list-item">
                                <div className="flex items-center">
                                    <div className="mr-4 w-8 h-8">
                                        <CountryFlag
                                            countryCode={country.code}
                                            width={32}
                                            alt={country.name}
                                        />
                                    </div>
                                    <span className="text-base">{country.name}</span>
                                </div>
                                <FiChevronRight className="text-gray-400" />
                            </div>
                        </Link>
                    ))
                )}
            </div>

            {/* Masaüstü görünüm için grid */}
            <div className="desktop-countries-grid">
                {isLoadingCountries ? (
                    // Yükleme durumunda gösterilecek içerik (masaüstü)
                    Array(6).fill(0).map((_, index) => (
                        <div key={index} className="animate-pulse desktop-country-card">
                            <div className="flex items-center space-x-4">
                                <div className="rounded-full bg-gray-200 h-12 w-12"></div>
                                <div className="h-5 bg-gray-200 rounded w-32"></div>
                            </div>
                            <div className="h-5 w-5 bg-gray-200 rounded-full"></div>
                        </div>
                    ))
                ) : (
                    // Ülkeler yüklendiğinde gösterilecek içerik (masaüstü)
                    allCountries.map(country => (
                        <Link href={`/${i18n.language}/${country.id}-esim`} key={country.id}>
                            <div className="desktop-country-card">
                                <div className="flex items-center">
                                    <div className="mr-4 w-10 h-10">
                                        <CountryFlag
                                            countryCode={country.code}
                                            width={40}
                                            alt={country.name}
                                        />
                                    </div>
                                    <span className="text-base font-medium">{country.name}</span>
                                </div>
                                <FiChevronRight className="text-gray-400 h-5 w-5" />
                            </div>
                        </Link>
                    ))
                )}
            </div>

            {/* eSIM Uyumluluk Modal Component */}
            <ESIMCompatibilityModal
                isOpen={showCompatibilityModal}
                onClose={closeCompatibilityModal}
            />

            {/* eSIM Success Screen */}
            {showSuccessScreen && (
                <ESIMReadyScreen
                    onClose={closeSuccessScreen}
                />
            )}
        </div>
    );
}